# TradeFusion当前项目结构

## 概述

本文档基于MCP工具分析生成，展示TradeFusion项目在删除触发器系统后的当前结构状态。

**生成时间**: 2025-07-22  
**分析工具**: MCP数据库工具 + 目录结构分析  
**清理状态**: ✅ 触发器系统已完全清理

## 🗂️ 核心目录结构

### 📊 数据库模块
```
数据库0_实体模块/          # 数据库核心模块
├── 股票数据.db           # 主数据库文件
├── 数据库结构报告生成器.py
├── 执行索引优化.py
└── 各种数据库维护脚本

数据库1_基础表模块/        # 基础表处理模块
数据库2_板块层统计/        # 板块层统计分析
├── 板块涨停表.py         # 板块涨停统计 (第2层数据处理)
├── 板块精选.py           # 板块精选算法 (第5层精选决策)
└── 相关日志和验证脚本

数据库3_个股所属板块层统计/ # 个股板块层统计
├── A3_所属板块评分表.py   # 所属板块评分
├── 个股接力表.py         # 个股接力算法
└── 相关日志文件

数据库写大智慧/            # 大智慧格式输出
├── 综合人气190.py        # 人气数据输出
├── 生成DZH3人气股票板块.py # DZH3格式输出
├── 接力.py              # 接力数据输出
└── 其他输出模块
```

### 📥 数据采集模块
```
数据1_本地采集/            # 本地数据采集
├── 采集_本地数据.py       # 本地DAT文件采集
└── 基础涨停数据表.py      # 涨停数据处理

数据2_网络采集/            # 网络数据采集
├── 人气_东财采集.py       # 东财人气采集 (10分钟间隔)
├── 人气_同花采集.py       # 同花人气采集 (5分钟间隔)
├── 个股人气表.py          # 人气数据融合
├── 人气临时表管理.py      # 临时表管理
└── 浏览器管理器.py        # 浏览器自动化

数据3_网络采集_bk/         # 选股宝数据采集
├── 选股宝抓取.py          # 选股宝数据抓取
├── 选股宝清洗.py          # 选股宝数据清洗
└── 个股解读_板块信息_关联表.py # 1对3表数据分发
```

### 🔧 公共模块
```
公共模块/                  # 公共功能模块
├── 交易日期.py            # 统一交易日期判断
├── 配置管理.py            # 配置管理
├── TradeFusion统一日志标准.py # 三色日志标准
├── 加代码前缀.py          # 股票代码标准化
├── 日志系统性能分析器.py   # 日志性能分析
└── 调度配置/              # 调度配置文件
    ├── 各模块.json        # 各模块调度配置
    └── 调度配置总览.md     # 配置说明
```

### 🛠️ 工具和脚本
```
环境清理工具/              # 环境清理工具集
├── TradeFusion环境清理器.py # 主清理工具
├── 数据库清理模块.py       # 数据库清理
├── 进程检查器.py          # 进程管理
└── 其他清理脚本

启动TradeFusion.py         # 主启动脚本
检查数据库状态.py          # 数据库状态检查
检查所有表状态.py          # 表状态检查
检查今日数据.py            # 今日数据检查
日志监控器.py              # 日志监控
```

### 📚 文档和配置
```
DOC/                      # 项目文档
├── TradeFusion数据流依赖关系分析.md
├── TradeFusion日志系统规范.md
├── 数据流思维导图/        # 思维导图集合
│   ├── 01_TradeFusion完整数据流架构图.mmd
│   ├── 02_TradeFusion核心数据流路径图.mmd
│   ├── 03_TradeFusion关键断点分析图.mmd
│   └── README.md
└── 其他技术文档

config/                   # 配置文件
├── data_sources.yaml     # 数据源配置
└── 其他配置文件

logs/                     # 日志文件目录
├── 各模块日志文件
└── startup.log           # 启动日志
```

## 🗄️ 数据库表结构

### 核心业务表 (10个)
- **个股连板高度表** - 本地采集数据，108条今日数据 ✅
- **个股人气表** - 东财+同花融合数据
- **个股解读表** - 选股宝个股解读
- **板块信息表** - 选股宝板块信息
- **个股板块关联表** - 股票-板块关联关系
- **所属板块评分表** - 个股所属板块评分
- **板块涨停表** - 板块涨停统计
- **板块精选表** - 精选板块算法结果
- **个股接力表** - 个股接力算法结果
- **DAT文件处理记录表** - DAT文件处理记录

### 临时表 (4个)
- **临时表_东财人气** - 东财人气临时数据，100条记录 ✅
- **临时表_同花人气** - 同花人气临时数据，34条记录 ✅
- **临时表_选股宝原始** - 选股宝原始数据，1条记录 ✅
- **临时表_选股宝清洗** - 选股宝清洗数据

## 🔄 当前数据流状态

### ✅ 自动化数据流
1. **选股宝数据流** (完全自动化):
   ```
   选股宝抓取 → 自动触发清洗 → 自动触发关联表处理
   ```

2. **人气数据流** (独立自动化):
   ```
   东财采集 → 自动触发人气融合
   同花采集 → 自动触发人气融合
   ```

3. **本地数据流** (新增自动化):
   ```
   本地数据采集 → 自动触发板块涨停统计
   ```

### ❌ 需要手动执行的模块
- 所属板块评分表.py (第4层)
- 板块精选.py (第5层)
- 个股接力表.py (第5层)
- 所有输出模块 (第6层)

## 🧹 清理完成状态

### ✅ 已清理的触发器相关文件
- 字段级触发器系统目录 → 移至备份目录
- 启动字段级触发器系统.py → 已删除
- __pycache__/启动字段级触发器系统.cpython-312.pyc → 已删除
- 轻量级调度管理器.py → 已删除 (依赖文件不存在)

### 📁 备份目录
- `备份_20250722_104731/字段级触发器系统/` - 完整触发器系统备份
- `备份_20250722_105334/字段级触发器系统/` - 另一个备份版本

## 🎯 系统特点

### 优势
1. **架构清晰** - 6层数据流架构明确
2. **部分自动化** - 关键数据流已实现自动触发
3. **日志规范** - 统一的三色日志标准
4. **模块解耦** - 各模块独立运行，依赖关系清晰

### 待优化
1. **手动执行** - 部分模块仍需手动触发
2. **监控缺失** - 缺少自动化的数据流监控
3. **错误处理** - 需要更完善的异常处理机制

## 📈 下一步建议

1. **完善自动触发** - 为剩余模块添加自动触发机制
2. **建立监控** - 实现数据流状态监控和告警
3. **优化性能** - 基于当前架构进行性能优化
4. **文档维护** - 持续更新技术文档

---
*文档生成时间：2025-07-22*  
*基于MCP工具实时分析结果*  
*触发器系统清理状态：✅ 完成*
