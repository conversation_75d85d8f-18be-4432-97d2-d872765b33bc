# TradeFusion数据流结构分析报告

## 📋 项目概述

本报告基于对TradeFusion项目16个业务模块的深入代码分析，完整梳理了数据流结构和模块间的调用关系，生成了清晰的数据流结构思维导图。

## 🎯 分析目标

1. **数据流关系识别**：分析每个模块的数据库表输入输出关系
2. **调用关系识别**：分析模块间的_trigger_函数调用关系  
3. **思维导图生成**：创建清晰反映上游下游数据表使用关系的可视化图表

## 📊 数据流结构思维导图

### 设计规范
- **节点类型**：模块用方块[]表示，数据库表用圆柱体[()]表示
- **连接线类型**：数据流用实线-->表示，调用关系用虚线-.->表示
- **布局结构**：7层树形结构，从上到下展示时间顺序
- **颜色区分**：不同层级和类型使用不同颜色标识

### 架构层级

#### 🔵 第1层：数据采集层（并行执行）
- **A1: 采集_本地数据.py** - 本地DAT文件采集，12秒高频执行
- **A2: 人气_东财采集.py** - 东财网站爬虫，10分钟执行
- **A3: 人气_同花采集.py** - 同花顺网站爬虫，5分钟执行  
- **A4: 选股宝抓取.py** - 选股宝网站爬虫，手动执行

#### 🟣 第2层：数据处理层（依赖第1层）
- **B1: 选股宝清洗.py** - 清洗选股宝原始数据
- **B2: 个股人气表.py** - 融合东财和同花人气数据
- **B3: 板块涨停表.py** - 统计板块涨停评分

#### 🟢 第3层：数据加工层（多阶段处理）
- **C1: 个股解读_板块信息_关联表.py** - 生成三张基础关联表
- **C2: A3_所属板块评分表.py** - 计算个股所属板块评分
- **C3: 板块精选.py** - 基于历史数据筛选精选板块
- **C4: 个股接力表.py** - 计算个股接力值

#### 🟠 第4层：输出应用层（生成最终文件）
- **D1: 综合人气190.py** - 生成190人气.dat文件
- **D2: 生成DZH3人气板块.py** - 生成自选股人气.BLK文件
- **D3: 接力.py** - 生成接力.dat文件
- **D4: A4_所属板块强度.py** - 生成所属板块强度.dat文件
- **D5: 选股宝_大智慧str.py** - 生成选股宝STR文件

## 🔄 主要数据流路径

### 路径1：选股宝数据流
```
A4(选股宝抓取) → B1(选股宝清洗) → C1(个股解读_板块信息_关联表) → D5(选股宝_大智慧str)
```
**数据表流转**：临时表_选股宝原始 → 临时表_选股宝清洗 → 个股解读表+板块信息表+个股板块关联表

### 路径2：人气数据流  
```
A2(人气_东财采集) + A3(人气_同花采集) → B2(个股人气表) → D1(综合人气190) + D2(生成DZH3人气板块)
```
**数据表流转**：临时表_东财人气+临时表_同花人气 → 个股人气表

### 路径3：板块涨停数据流
```
A1(采集_本地数据) → B3(板块涨停表) → C2(A3_所属板块评分表) → C3(板块精选) → C4(个股接力表) → D3(接力)
                                    ↓
                                   D4(A4_所属板块强度)
```
**数据表流转**：个股连板高度表 → 板块涨停表 → 所属板块评分表 → 板块精选表 → 个股接力表

## 📈 数据库表分类统计

### 临时表（4张）
- 临时表_东财人气：存储东财爬虫数据
- 临时表_同花人气：存储同花爬虫数据  
- 临时表_选股宝原始：存储选股宝原始网页数据
- 临时表_选股宝清洗：存储选股宝清洗后结构化数据

### 正式表（9张）
- 个股连板高度表：本地DAT文件数据
- 个股人气表：融合后的人气排名数据
- 板块涨停表：板块涨停统计数据
- 个股解读表：个股解读文本数据
- 板块信息表：板块基础信息数据
- 个股板块关联表：个股与板块的关联关系
- 所属板块评分表：个股所属板块综合评分
- 板块精选表：精选板块列表
- 个股接力表：个股接力值计算结果

## 🔗 关键调用关系链

### 触发器系统
每个模块完成数据处理后，通过_trigger_函数自动调用下游模块，形成完整的数据处理链：

1. **A1** -.-> **B3** -.-> **C2** -.-> **C3** -.-> **C4** -.-> **D3**
2. **A2/A3** -.-> **B2** -.-> **D1/D2**  
3. **A4** -.-> **B1** -.-> **C1** -.-> **D5**
4. **C2** -.-> **D4**

### 数据依赖关系
- B3依赖A1的个股连板高度表和C1的个股板块关联表
- C4需要读取4张表：板块精选表、个股人气表、个股板块关联表、板块涨停表
- A3需要查询个股人气表来获取股票代码映射

## ✅ 分析结论

1. **架构清晰**：7层架构设计合理，数据流向明确
2. **解耦良好**：模块间通过数据库表和触发器系统实现松耦合
3. **数据完整**：临时表机制确保数据处理的可靠性和可追溯性
4. **流程自动化**：触发器系统实现了完全自动化的数据处理流程

## 📝 技术特点

- **临时表机制**：采集模块写入临时表，处理模块读取临时表，实现数据缓冲
- **状态管理**：通过处理状态字段跟踪数据处理进度
- **自动清理**：临时表支持自动清理历史数据
- **容错设计**：支持重复处理和错误恢复
- **三色日志**：统一的日志格式便于监控和调试

---

*本报告基于2025-07-23的代码分析生成，完整反映了TradeFusion项目的数据流结构和模块关系。*
