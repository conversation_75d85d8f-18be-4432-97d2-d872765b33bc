# 文件名称变更表

## 说明

记录项目中所有文件和目录的名称变更，便于追踪依赖关系和更新引用。

## 变更记录

### 目录变更

（暂无记录）

### 文件变更

- A1_大智慧_连板高度.py --> 采集_本地数据.py
- GPDM_CZ.py --> 加代码前缀.py
- riqi.py --> 交易日期.py
- A2_个股连板高度表.py --> 基础涨停数据表.py
- browser_manager.py --> 浏览器管理器.py

### 新增文件

- 公共模块/配置管理.py --> 新增配置管理模块
- 公共模块/日志配置.py --> 新增统一日志配置模块
- 公共模块/日志检查工具.py --> 新增日志规范检查工具
- 公共模块/新文件检查脚本.py --> 新增集成文件检查脚本
- config/data_sources.yaml --> 新增YAML配置文件

### 删除文件

- 公共模块/日志配置.py --> 删除旧日志系统，为TradeFusion智能日志系统让路

### 引用更新记录

**2025-01-10 引用更新批次：**
- 修复所有"公用"模块引用 → "公共模块"
- 修复所有"GPDM_CZ"引用 → "加代码前缀"
- 修复所有"riqi"引用 → "交易日期"
- 涉及文件：11个Python文件 + 1个Markdown文档
- 修复内容：导入语句、函数调用、路径配置、注释引用

---
**更新日期：** 2025-01-10
**维护说明：** 每次文件或目录重命名后必须更新此表
